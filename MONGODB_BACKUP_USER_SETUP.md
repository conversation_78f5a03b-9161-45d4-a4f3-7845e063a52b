# 🔧 MongoDB Backup User Setup Guide

## 📋 Quick Setup

This guide provides step-by-step instructions to create a dedicated MongoDB user specifically for backup operations.

## 🎯 Problem Statement

The current backup system fails because:
- The main application user (`generalWeb`) lacks `listDatabases` permission
- Granting broad permissions to the application user is a security risk
- Need a dedicated backup user with minimal required permissions

## ✅ Solution: Dedicated Backup User

Create a separate user (`backupUser`) with only the permissions needed for backup operations.

## 🚀 Step-by-Step Setup

### **Step 1: Connect to MongoDB as Admin**

```bash
# Connect to MongoDB with admin privileges
mongo --host localhost:27017 -u admin -p --authenticationDatabase admin
```

### **Step 2: Create Backup User**

```javascript
use admin

// Create dedicated backup user
db.createUser({
  user: "backupUser",
  pwd: "SecureBackupPassword123!",  // Use a strong password
  roles: [
    // Permission to list all databases
    { role: "listDatabases", db: "admin" },
    
    // ReadWrite access to all tenant databases
    { role: "readWrite", db: "generalWeb" },
    { role: "readWrite", db: "generalWebDemo" },
    { role: "readWrite", db: "generalWebNewcitymobile" },
    { role: "readWrite", db: "generalWebWanigarathna" }
    
    // Add more tenant databases as needed:
    // { role: "readWrite", db: "generalWebNewTenant" }
  ]
})
```

### **Step 3: Verify Backup User**

```javascript
// Check user was created successfully
db.getUser("backupUser")

// Test listDatabases permission
db.runCommand({listDatabases: 1})

// Test access to tenant databases
use generalWebDemo
db.runCommand({ping: 1})
```

### **Step 4: Test Database Discovery**

```javascript
// Test the backup user can discover tenant databases
use admin

var dbs = db.runCommand({listDatabases: 1});
print("Total databases: " + dbs.databases.length);

// Filter for tenant databases
var tenantDbs = dbs.databases.filter(db => db.name.startsWith('generalWeb'));
print("Tenant databases found: " + tenantDbs.length);
tenantDbs.forEach(db => print("  ✅ " + db.name));
```

## ⚙️ Application Configuration

### **Update application.properties**

```properties
# Main application database connection
spring.data.mongodb.username=generalWeb
spring.data.mongodb.password=your_app_password
spring.data.mongodb.authDatabase=admin

# Backup user configuration
backup.mongodb.username=backupUser
backup.mongodb.password=SecureBackupPassword123!
backup.mongodb.authDatabase=admin
```

### **Update Backup Service**

Add backup user configuration to your Java service:

```java
@Value("${backup.mongodb.username:#{null}}")
private String backupUsername;

@Value("${backup.mongodb.password:#{null}}")
private String backupPassword;

@Value("${backup.mongodb.authDatabase:admin}")
private String backupAuthDatabase;
```

## 🔄 Adding New Tenant Databases

When you add a new tenant database, grant access to the backup user:

```javascript
use admin

// Grant readWrite access to new tenant database
db.grantRolesToUser("backupUser", [
  { role: "readWrite", db: "generalWebNewTenant" }
])

// Verify access
use generalWebNewTenant
db.runCommand({ping: 1})
```

## 🧪 Testing Commands

### **Test Backup User Login**

```bash
# Test backup user can connect
mongo --host localhost:27017 -u backupUser -p SecureBackupPassword123! --authenticationDatabase admin
```

### **Test Database Access**

```javascript
// Test all tenant databases are accessible
["generalWeb", "generalWebDemo", "generalWebNewcitymobile", "generalWebWanigarathna"].forEach(function(dbName) {
  try {
    var result = db.getSiblingDB(dbName).runCommand({ping: 1});
    print(dbName + ": " + (result.ok ? "✅ ACCESSIBLE" : "❌ FAILED"));
  } catch(e) {
    print(dbName + ": ❌ ERROR - " + e.message);
  }
});
```

## 🔐 Security Notes

1. **Strong Password**: Use a complex password for the backup user
2. **Limited Scope**: Backup user only has access to tenant databases
3. **No Admin Rights**: Backup user cannot create/drop databases or users
4. **Audit Trail**: All backup user actions are logged

## 🚨 Troubleshooting

### **Issue: "not authorized" error**
```javascript
// Check user roles
use admin
db.getUser("backupUser")

// Verify listDatabases permission
db.runCommand({listDatabases: 1})
```

### **Issue: Cannot access tenant database**
```javascript
// Grant missing database access
use admin
db.grantRolesToUser("backupUser", [
  { role: "readWrite", db: "missingDatabaseName" }
])
```

### **Issue: Backup discovery returns empty list**
```javascript
// Test database listing manually
use admin
var result = db.runCommand({listDatabases: 1});
print("Command result: " + JSON.stringify(result, null, 2));
```

## ✅ Verification Checklist

- [ ] Backup user created successfully
- [ ] User can list all databases
- [ ] User can access all tenant databases
- [ ] Application configuration updated
- [ ] Backup service uses new credentials
- [ ] Automated backup discovery works
- [ ] Manual backup test successful

## 📚 Related Documentation

- [MONGODB_MULTI_TENANT_SETUP.md](./MONGODB_MULTI_TENANT_SETUP.md) - Complete multi-tenant setup
- [MongoDB User Management](https://docs.mongodb.com/manual/tutorial/manage-users-and-roles/)
- [MongoDB Built-in Roles](https://docs.mongodb.com/manual/reference/built-in-roles/)

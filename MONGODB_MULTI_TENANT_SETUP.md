# 🏢 MongoDB Multi-Tenant Setup & Backup Configuration

## 📋 Overview

This document provides the complete setup for MongoDB multi-tenant permissions and automated backup system for the ERP application.

## 🔧 MongoDB User Permissions Setup

### **Current Issue**
The `generalWeb` user only has `readWrite` permissions on the main `generalWeb` database, but needs access to all tenant databases for:
- Multi-tenant application functionality
- Automated backup discovery and execution

### **Required Permissions**
1. **listDatabases** - To discover all tenant databases automatically
2. **readWrite** - On all tenant databases (generalWeb, generalWebDemo, generalWebNewcitymobile, etc.)

## 🚀 MongoDB Permission Queries

### **Method 1: Grant Individual Database Access (Recommended)**

```javascript
// Connect to MongoDB as admin user
use admin

// Grant listDatabases permission for backup discovery
db.grantRolesToUser("generalWeb", [
  { role: "listDatabases", db: "admin" }
])

// Grant readWrite access to all tenant databases
db.grantRolesToUser("generalWeb", [
  { role: "readWrite", db: "generalWebDemo" },
  { role: "readWrite", db: "generalWebNewcitymobile" },
  { role: "readWrite", db: "generalWebWanigarathna" }
  // Add more tenant databases as they are created
])
```

### **Method 2: Create Custom Multi-Tenant Role**

```javascript
use admin

// Create a comprehensive multi-tenant role
db.createRole({
  role: "multiTenantBackupRole",
  privileges: [
    // Cluster-level permissions
    {
      resource: { cluster: true },
      actions: ["listDatabases"]
    },
    // Database-level permissions for all generalWeb* databases
    {
      resource: { db: "generalWeb", collection: "" },
      actions: ["find", "insert", "update", "remove", "createIndex", "dropIndex", "listCollections"]
    },
    {
      resource: { db: "generalWebDemo", collection: "" },
      actions: ["find", "insert", "update", "remove", "createIndex", "dropIndex", "listCollections"]
    },
    {
      resource: { db: "generalWebNewcitymobile", collection: "" },
      actions: ["find", "insert", "update", "remove", "createIndex", "dropIndex", "listCollections"]
    },
    {
      resource: { db: "generalWebWanigarathna", collection: "" },
      actions: ["find", "insert", "update", "remove", "createIndex", "dropIndex", "listCollections"]
    }
  ],
  roles: []
})

// Grant the custom role to generalWeb user
db.grantRolesToUser("generalWeb", [
  { role: "multiTenantBackupRole", db: "admin" }
])
```

### **Method 3: Grant Broad Access (Less Secure)**

```javascript
use admin

// Grant readWriteAnyDatabase (use with caution)
db.grantRolesToUser("generalWeb", [
  { role: "readWriteAnyDatabase", db: "admin" }
])
```

## 🔍 Verification Queries

### **Check User Permissions**
```javascript
use admin

// View user details and roles
db.getUser("generalWeb")

// Test listDatabases permission
db.runCommand({listDatabases: 1})

// Test access to specific tenant database
use generalWebDemo
db.runCommand({ping: 1})
```

### **Test Database Discovery**
```javascript
// List all databases and filter for tenant databases
db.adminCommand('listDatabases').databases.filter(db => db.name.startsWith('generalWeb'))

// Alternative filtering approach
db.adminCommand('listDatabases').databases.forEach(function(db) { 
  if(db.name.match(/^generalWeb/)) print(db.name); 
})
```

## 📊 Adding New Tenant Databases

When adding a new tenant (e.g., `generalWebNewClient`):

### **Option 1: Grant Individual Access**
```javascript
use admin
db.grantRolesToUser("generalWeb", [
  { role: "readWrite", db: "generalWebNewClient" }
])
```

### **Option 2: Update Custom Role**
```javascript
use admin
db.updateRole("multiTenantBackupRole", {
  privileges: [
    // ... existing privileges ...
    {
      resource: { db: "generalWebNewClient", collection: "" },
      actions: ["find", "insert", "update", "remove", "createIndex", "dropIndex", "listCollections"]
    }
  ]
})
```

## 🔐 Security Best Practices

1. **Principle of Least Privilege**: Only grant necessary permissions
2. **Regular Audits**: Periodically review user permissions
3. **Role-Based Access**: Use custom roles instead of built-in broad roles
4. **Documentation**: Keep track of all granted permissions

## 🚨 Troubleshooting

### **Common Issues**

1. **"not authorized" errors**: User lacks required permissions
2. **Empty database list**: User can't execute listDatabases command
3. **Backup failures**: User can't read from tenant databases

### **Debug Commands**
```javascript
// Check current user's effective permissions
db.runCommand({connectionStatus: 1})

// Test specific database access
use generalWebDemo
db.runCommand({listCollections: 1})

// Verify backup user can access all tenant databases
["generalWeb", "generalWebDemo", "generalWebNewcitymobile", "generalWebWanigarathna"].forEach(function(dbName) {
  try {
    var result = db.getSiblingDB(dbName).runCommand({ping: 1});
    print(dbName + ": " + (result.ok ? "✅ OK" : "❌ FAILED"));
  } catch(e) {
    print(dbName + ": ❌ ERROR - " + e.message);
  }
});
```

## 📝 Implementation Checklist

- [ ] Connect to MongoDB as admin user
- [ ] Run permission grant queries
- [ ] Verify user can list databases
- [ ] Test access to all tenant databases
- [ ] Update application configuration if needed
- [ ] Test automated backup discovery
- [ ] Document any new tenant databases added

## 🔄 Maintenance

- **When adding new tenants**: Update user permissions
- **Regular reviews**: Audit permissions quarterly
- **Backup testing**: Verify backup system can access all databases
- **Documentation**: Keep this document updated with new tenant databases

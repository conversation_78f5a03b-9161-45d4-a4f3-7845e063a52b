# 🔧 MongoDB Backup User - Quick Reference Queries

## 🚀 Essential Setup Queries

### **1. Create Backup User**

```javascript
use admin

db.createUser({
  user: "backupUser",
  pwd: "SecureBackupPassword123!",
  roles: [
    { role: "listDatabases", db: "admin" },
    { role: "readWrite", db: "generalWeb" },
    { role: "readWrite", db: "generalWebDemo" },
    { role: "readWrite", db: "generalWebNewcitymobile" },
    { role: "readWrite", db: "generalWebWanigarathna" }
  ]
})
```

### **2. Verify User Creation**

```javascript
use admin
db.getUser("backupUser")
```

### **3. Test Database Listing**

```javascript
use admin
db.runCommand({listDatabases: 1})
```

### **4. Test Tenant Database Access**

```javascript
["generalWeb", "generalWebDemo", "generalWebNewcitymobile", "generalWebWanigarathna"].forEach(function(dbName) {
  try {
    var result = db.getSiblingDB(dbName).runCommand({ping: 1});
    print(dbName + ": " + (result.ok ? "✅ OK" : "❌ FAILED"));
  } catch(e) {
    print(dbName + ": ❌ ERROR - " + e.message);
  }
});
```

## 🔄 Adding New Tenant Database

```javascript
use admin
db.grantRolesToUser("backupUser", [
  { role: "readWrite", db: "generalWebNewTenant" }
])
```

## 🧪 Testing Queries

### **Test Backup User Login**

```bash
mongo --host localhost:27017 -u backupUser -p SecureBackupPassword123! --authenticationDatabase admin
```

### **Test Database Discovery**

```javascript
use admin
var dbs = db.runCommand({listDatabases: 1});
var tenantDbs = dbs.databases.filter(db => db.name.startsWith('generalWeb'));
print("Found " + tenantDbs.length + " tenant databases:");
tenantDbs.forEach(db => print("  - " + db.name));
```

## 🔧 Alternative: Custom Role Approach

### **Create Custom Role**

```javascript
use admin

db.createRole({
  role: "tenantBackupRole",
  privileges: [
    {
      resource: { cluster: true },
      actions: ["listDatabases"]
    },
    {
      resource: { db: "generalWeb", collection: "" },
      actions: ["find", "insert", "update", "remove", "listCollections"]
    },
    {
      resource: { db: "generalWebDemo", collection: "" },
      actions: ["find", "insert", "update", "remove", "listCollections"]
    },
    {
      resource: { db: "generalWebNewcitymobile", collection: "" },
      actions: ["find", "insert", "update", "remove", "listCollections"]
    },
    {
      resource: { db: "generalWebWanigarathna", collection: "" },
      actions: ["find", "insert", "update", "remove", "listCollections"]
    }
  ],
  roles: []
})
```

### **Create User with Custom Role**

```javascript
use admin

db.createUser({
  user: "backupUser",
  pwd: "SecureBackupPassword123!",
  roles: [
    { role: "tenantBackupRole", db: "admin" }
  ]
})
```

## 🚨 Troubleshooting Queries

### **Check User Permissions**

```javascript
use admin
db.runCommand({usersInfo: "backupUser", showPrivileges: true})
```

### **Test Connection Status**

```javascript
db.runCommand({connectionStatus: 1})
```

### **Debug Database Access**

```javascript
use generalWebDemo
try {
  db.runCommand({listCollections: 1})
  print("✅ Can list collections")
} catch(e) {
  print("❌ Cannot list collections: " + e.message)
}
```

## 📋 Quick Commands Summary

```bash
# Connect as admin
mongo --host localhost:27017 -u admin -p --authenticationDatabase admin

# Connect as backup user
mongo --host localhost:27017 -u backupUser -p SecureBackupPassword123! --authenticationDatabase admin

# Test mongodump with backup user
mongodump --host localhost:27017 --username backupUser --password SecureBackupPassword123! --authenticationDatabase admin --db generalWebDemo --out ./test-backup
```

## ⚙️ Application Properties

```properties
# Backup user configuration
backup.mongodb.username=backupUser
backup.mongodb.password=SecureBackupPassword123!
backup.mongodb.authDatabase=admin
backup.mongodb.host=localhost
backup.mongodb.port=27017
```

## 🔐 Security Reminders

- ✅ Use strong passwords
- ✅ Limit permissions to only what's needed
- ✅ Regular permission audits
- ✅ Document all changes
- ❌ Don't use admin user for backups
- ❌ Don't grant unnecessary broad permissions

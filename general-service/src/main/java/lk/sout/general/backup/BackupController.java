package lk.sout.general.backup;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Arrays;

/**
 * Controller for backup management
 */
@RestController
@RequestMapping("/backup")
@PreAuthorize("hasRole('ADMIN')")
public class BackupController {

    @Autowired
    private ScheduledBackupService scheduledBackupService;

    @Autowired
    private MongoBackupService mongoBackupService;

    /**
     * Get backup status and configuration
     */
    @GetMapping("/status")
    public ResponseEntity<?> getBackupStatus() {
        try {
            ScheduledBackupService.BackupStatus status = scheduledBackupService.getBackupStatus();
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Failed to get backup status");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * Trigger manual backup
     */
    @PostMapping("/manual")
    public ResponseEntity<?> triggerManualBackup() {
        try {
            // Run backup in background thread to avoid timeout
            new Thread(() -> {
                scheduledBackupService.performManualBackup();
            }).start();

            Map<String, Object> response = new HashMap<>();
            response.put("message", "Manual backup started");
            response.put("status", "STARTED");
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Failed to start manual backup");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * Get backup configuration info
     */
    @GetMapping("/config")
    public ResponseEntity<?> getBackupConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("schedule", "Daily at 3:00 AM");
        config.put("cronExpression", "0 0 3 * * ?");
        config.put("retentionPolicy", "7 days");
        config.put("backupLocation", "Google Drive");
        config.put("compressionFormat", "tar.gz");

        return ResponseEntity.ok(config);
    }

    /**
     * Test database discovery
     */
    @GetMapping("/discover-databases")
    public ResponseEntity<?> discoverDatabases() {
        try {
            String[] databases = mongoBackupService.getDatabaseList();

            Map<String, Object> result = new HashMap<>();
            result.put("discoveredDatabases", Arrays.asList(databases));
            result.put("totalCount", databases.length);
            result.put("pattern", "generalWeb*");
            result.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Failed to discover databases");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * Debug MongoDB connection and list all databases
     */
    @GetMapping("/debug-mongodb")
    public ResponseEntity<?> debugMongoDB() {
        try {
            Map<String, Object> debug = new HashMap<>();

            // Test MongoDB connection
            debug.put("connectionTest", "Attempting to connect...");

            // Try to list all databases (not just generalWeb ones)
            String[] allDatabases = mongoBackupService.getAllDatabasesForDebug();
            debug.put("allDatabases", Arrays.asList(allDatabases));
            debug.put("totalDatabaseCount", allDatabases.length);

            // Get the filtered list
            String[] filteredDatabases = mongoBackupService.getDatabaseList();
            debug.put("filteredDatabases", Arrays.asList(filteredDatabases));
            debug.put("filteredCount", filteredDatabases.length);

            debug.put("timestamp", System.currentTimeMillis());
            debug.put("status", "SUCCESS");

            return ResponseEntity.ok(debug);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "MongoDB debug failed");
            error.put("message", e.getMessage());
            error.put("stackTrace", e.getStackTrace());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * Show MongoDB queries for debugging database discovery issues
     */
    @GetMapping("/show-queries")
    public ResponseEntity<?> showQueries() {
        Map<String, Object> queries = new HashMap<>();

        // Add test queries for debugging
        queries.put("betterQuery", "db.adminCommand({\"listDatabases\": 1, \"filter\": {\"name\": /^generalWeb/}})");
        queries.put("actualQuery", "db.adminCommand({\"listDatabases\": 1})");

        // Add test queries for NoSQL Booster or MongoDB Compass
        String[] testQueries = {
            "use admin",
            "db.runCommand({listDatabases: 1})",
            "db.runCommand({listDatabases: 1}).databases.filter(db => db.name.startsWith('generalWeb'))",
            "db.runCommand({listDatabases: 1}).databases.forEach(db => print(db.name))",
            "db.runCommand({connectionStatus: 1})",
            "db.getUser('generalWeb')",
            "db.runCommand({usersInfo: 'generalWeb', showPrivileges: true})",
            "db.getSiblingDB('generalWeb').runCommand({ping: 1})"
        };
        queries.put("testInNoSQLBooster", Arrays.asList(testQueries));

        queries.put("alternativeQuery", "db.runCommand({\"listCollections\": 1})");

        // Add current configuration info
        Map<String, Object> config = new HashMap<>();
        config.put("mongoHost", "*************");
        config.put("mongoPort", 27017);
        config.put("authDatabase", "admin");
        config.put("username", "generalWeb");
        config.put("databasePattern", "generalWeb*");
        queries.put("currentConfig", config);

        // Add discovered databases from service
        try {
            String[] discoveredDbs = mongoBackupService.getDatabaseList();
            queries.put("discoveredDatabases", Arrays.asList(discoveredDbs));
            queries.put("discoveredCount", discoveredDbs.length);
        } catch (Exception e) {
            queries.put("discoveryError", e.getMessage());
        }

        return ResponseEntity.ok(queries);
    }

    /**
     * Test MongoDB backup only (without Google Drive)
     */
    @PostMapping("/test-mongo-backup")
    public ResponseEntity<?> testMongoBackup() {
        try {
            Map<String, Object> result = new HashMap<>();

            // Test database discovery first
            String[] databases = mongoBackupService.getDatabaseList();
            result.put("discoveredDatabases", Arrays.asList(databases));
            result.put("databaseCount", databases.length);

            if (databases.length == 0) {
                result.put("status", "ERROR");
                result.put("message", "No databases discovered for backup");
                return ResponseEntity.ok(result);
            }

            // Create MongoDB backup (without Google Drive upload)
            result.put("status", "CREATING_BACKUP");
            result.put("message", "Creating MongoDB backup...");

            // Run backup in background thread
            new Thread(() -> {
                try {
                    java.io.File backupFile = mongoBackupService.createBackup();
                    logger.info("✅ Test backup created successfully: {}", backupFile.getName());
                    logger.info("📁 Backup file size: {} MB", backupFile.length() / 1024 / 1024);
                    logger.info("📍 Backup file location: {}", backupFile.getAbsolutePath());
                } catch (Exception e) {
                    logger.error("❌ Test backup failed: {}", e.getMessage(), e);
                }
            }).start();

            result.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Test backup failed");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
}

package lk.sout.general.backup;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Arrays;

/**
 * Controller for backup management
 */
@RestController
@RequestMapping("/backup")
@PreAuthorize("hasRole('ADMIN')")
public class BackupController {

    @Autowired
    private ScheduledBackupService scheduledBackupService;

    @Autowired
    private MongoBackupService mongoBackupService;

    /**
     * Get backup status and configuration
     */
    @GetMapping("/status")
    public ResponseEntity<?> getBackupStatus() {
        try {
            ScheduledBackupService.BackupStatus status = scheduledBackupService.getBackupStatus();
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Failed to get backup status");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * Trigger manual backup
     */
    @PostMapping("/manual")
    public ResponseEntity<?> triggerManualBackup() {
        try {
            // Run backup in background thread to avoid timeout
            new Thread(() -> {
                scheduledBackupService.performManualBackup();
            }).start();

            Map<String, Object> response = new HashMap<>();
            response.put("message", "Manual backup started");
            response.put("status", "STARTED");
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Failed to start manual backup");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * Get backup configuration info
     */
    @GetMapping("/config")
    public ResponseEntity<?> getBackupConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("schedule", "Daily at 3:00 AM");
        config.put("cronExpression", "0 0 3 * * ?");
        config.put("retentionPolicy", "7 days");
        config.put("backupLocation", "Google Drive");
        config.put("compressionFormat", "tar.gz");

        return ResponseEntity.ok(config);
    }

    /**
     * Test database discovery
     */
    @GetMapping("/discover-databases")
    public ResponseEntity<?> discoverDatabases() {
        try {
            String[] databases = mongoBackupService.getDatabaseList();

            Map<String, Object> result = new HashMap<>();
            result.put("discoveredDatabases", Arrays.asList(databases));
            result.put("totalCount", databases.length);
            result.put("pattern", "generalWeb*");
            result.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Failed to discover databases");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * Debug MongoDB connection and list all databases
     */
    @GetMapping("/debug-mongodb")
    public ResponseEntity<?> debugMongoDB() {
        try {
            Map<String, Object> debug = new HashMap<>();

            // Test MongoDB connection
            debug.put("connectionTest", "Attempting to connect...");

            // Try to list all databases (not just generalWeb ones)
            String[] allDatabases = mongoBackupService.getAllDatabasesForDebug();
            debug.put("allDatabases", Arrays.asList(allDatabases));
            debug.put("totalDatabaseCount", allDatabases.length);

            // Get the filtered list
            String[] filteredDatabases = mongoBackupService.getDatabaseList();
            debug.put("filteredDatabases", Arrays.asList(filteredDatabases));
            debug.put("filteredCount", filteredDatabases.length);

            debug.put("timestamp", System.currentTimeMillis());
            debug.put("status", "SUCCESS");

            return ResponseEntity.ok(debug);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "MongoDB debug failed");
            error.put("message", e.getMessage());
            error.put("stackTrace", e.getStackTrace());
            return ResponseEntity.status(500).body(error);
        }
    }
}
